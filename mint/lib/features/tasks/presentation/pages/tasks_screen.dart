import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mental_health/core/theme.dart';
import '../bloc/tasks_bloc.dart';
import '../bloc/tasks_event.dart';
import '../bloc/tasks_state.dart';
import '../widgets/task_stats_card.dart';
import '../widgets/task_item_card.dart';
import '../widgets/task_category_chip.dart';
import '../../domain/entities/task.dart';
import '../../domain/entities/task_category.dart';
import 'add_edit_task_screen.dart';

/// <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> vụ chuyên nghiệp
class TasksScreen extends StatefulWidget {
  const TasksScreen({super.key});

  @override
  State<TasksScreen> createState() => _TasksScreenState();
}

class _TasksScreenState extends State<TasksScreen>
    with TickerProviderStateMixin {
  late AnimationController _fabAnimationController;
  late AnimationController _headerAnimationController;
  TaskCategory? _selectedCategory;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _fabAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _headerAnimationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    // Load dữ liệu ban đầu
    context.read<TasksBloc>().add(const LoadTodayTasks());

    // Bắt đầu animation
    _headerAnimationController.forward();
    _fabAnimationController.forward();
  }

  @override
  void dispose() {
    _fabAnimationController.dispose();
    _headerAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      appBar: _buildAppbar(),
      body: CustomScrollView(
        slivers: [
          _buildSliverContent(),
        ],
      ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  PreferredSizeWidget _buildAppbar() {
    return AppBar(
      title: Align(
        alignment: Alignment.centerLeft,
        child: Row(
          spacing: 10,
          children: [
            Icon(Icons.list_alt_outlined, color: Colors.white, size: 24),
            const Text(
              'Nhiệm vụ hôm nay',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
      automaticallyImplyLeading: false,
      backgroundColor: Colors.transparent,
      elevation: 0,
      centerTitle: true,
      actions: [
        IconButton(
          icon: const Icon(Icons.search, color: Colors.white),
          onPressed: _showSearchDialog,
        ),
        PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert, color: Colors.white),
          onSelected: _handleMenuAction,
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'filter',
              child: Row(
                children: [
                  Icon(Icons.filter_list, color: Colors.white70),
                  SizedBox(width: 8),
                  Text('Lọc nhiệm vụ', style: TextStyle(color: Colors.white)),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'sort',
              child: Row(
                children: [
                  Icon(Icons.sort, color: Colors.white70),
                  SizedBox(width: 8),
                  Text('Sắp xếp', style: TextStyle(color: Colors.white)),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'delete_completed',
              child: Row(
                children: [
                  Icon(Icons.delete_sweep, color: Colors.red),
                  SizedBox(width: 8),
                  Text('Xóa đã hoàn thành',
                      style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
          color: Colors.black87,
        ),
      ],
    );
  }

  Widget _buildSliverContent() {
    return BlocBuilder<TasksBloc, TasksState>(
      builder: (context, state) {
        if (state is TasksLoading) {
          return const SliverFillRemaining(
            child: Center(
              child: CircularProgressIndicator(color: Colors.blue),
            ),
          );
        }

        if (state is TasksError) {
          return SliverFillRemaining(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline,
                    color: Colors.red,
                    size: 64,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Có lỗi xảy ra',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          color: Colors.white,
                        ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    state.message,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.white70,
                        ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      context.read<TasksBloc>().add(const RefreshTasks());
                    },
                    child: const Text('Thử lại'),
                  ),
                ],
              ),
            ),
          );
        }

        if (state is TasksLoaded) {
          return SliverList(
            delegate: SliverChildListDelegate([
              // Stats Card
              if (state.stats != null)
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: TaskStatsCard(stats: state.stats!),
                ),

              // Category Filter
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: TaskCategoryChipList(
                  categories: TaskCategory.defaultCategories,
                  selectedCategory: _selectedCategory,
                  onCategorySelected: (category) {
                    setState(() {
                      _selectedCategory =
                          category.id == 'all' ? null : category;
                    });
                    if (category.id == 'all') {
                      context.read<TasksBloc>().add(const LoadTodayTasks());
                    } else {
                      context
                          .read<TasksBloc>()
                          .add(LoadTasksByCategory(category.id));
                    }
                  },
                ),
              ),

              const SizedBox(height: 16),

              // Tasks List
              ...state.filteredAndSortedTasks.map((task) => Padding(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                    child: TaskItemCard(
                      task: task,
                      onTap: () => _showTaskDetails(task),
                      onToggleCompletion: () {
                        context
                            .read<TasksBloc>()
                            .add(ToggleTaskCompletion(task.id));
                      },
                      onEdit: () => _editTask(task),
                      onDelete: () => _deleteTask(task),
                    ),
                  )),

              if (state.filteredAndSortedTasks.isEmpty)
                Padding(
                  padding: const EdgeInsets.all(32),
                  child: Column(
                    children: [
                      const Icon(
                        Icons.task_alt,
                        color: Colors.white54,
                        size: 64,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Chưa có nhiệm vụ nào',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              color: Colors.white,
                            ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Hãy thêm nhiệm vụ đầu tiên của bạn!',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.white70,
                            ),
                      ),
                    ],
                  ),
                ),

              const SizedBox(height: 100), // Space for FAB
            ]),
          );
        }

        return const SliverFillRemaining(
          child: SizedBox.shrink(),
        );
      },
    );
  }

  Widget _buildFloatingActionButton() {
    return FloatingActionButton(onPressed: _addNewTask);
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.black87,
        title: const Text(
          'Tìm kiếm nhiệm vụ',
          style: TextStyle(color: Colors.white),
        ),
        content: TextField(
          style: const TextStyle(color: Colors.white),
          decoration: const InputDecoration(
            hintText: 'Nhập từ khóa...',
            hintStyle: TextStyle(color: Colors.white54),
            border: OutlineInputBorder(),
            enabledBorder: OutlineInputBorder(
              borderSide: BorderSide(color: Colors.white30),
            ),
            focusedBorder: OutlineInputBorder(
              borderSide: BorderSide(color: Colors.blue),
            ),
          ),
          onChanged: (query) {
            setState(() {
              _searchQuery = query;
            });
            context.read<TasksBloc>().add(SearchTasks(query));
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Đóng', style: TextStyle(color: Colors.white70)),
          ),
        ],
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'filter':
        _showFilterDialog();
        break;
      case 'sort':
        _showSortDialog();
        break;
      case 'delete_completed':
        _showDeleteCompletedDialog();
        break;
    }
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.black87,
        title: const Text(
          'Lọc nhiệm vụ',
          style: TextStyle(color: Colors.white),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: TaskFilter.values.map((filter) {
            String title;
            switch (filter) {
              case TaskFilter.all:
                title = 'Tất cả';
                break;
              case TaskFilter.completed:
                title = 'Đã hoàn thành';
                break;
              case TaskFilter.pending:
                title = 'Chưa hoàn thành';
                break;
              case TaskFilter.overdue:
                title = 'Quá hạn';
                break;
              case TaskFilter.today:
                title = 'Hôm nay';
                break;
              case TaskFilter.upcoming:
                title = 'Sắp đến hạn';
                break;
            }

            return ListTile(
              title: Text(title, style: const TextStyle(color: Colors.white)),
              onTap: () {
                context.read<TasksBloc>().add(ChangeTaskFilter(filter));
                Navigator.pop(context);
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  void _showSortDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.black87,
        title: const Text(
          'Sắp xếp nhiệm vụ',
          style: TextStyle(color: Colors.white),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: TaskSortOrder.values.map((sortOrder) {
            String title;
            switch (sortOrder) {
              case TaskSortOrder.createdDateAsc:
                title = 'Ngày tạo (cũ → mới)';
                break;
              case TaskSortOrder.createdDateDesc:
                title = 'Ngày tạo (mới → cũ)';
                break;
              case TaskSortOrder.priorityAsc:
                title = 'Độ ưu tiên (thấp → cao)';
                break;
              case TaskSortOrder.priorityDesc:
                title = 'Độ ưu tiên (cao → thấp)';
                break;
              case TaskSortOrder.dueDateAsc:
                title = 'Hạn chót (sớm → muộn)';
                break;
              case TaskSortOrder.dueDateDesc:
                title = 'Hạn chót (muộn → sớm)';
                break;
              case TaskSortOrder.titleAsc:
                title = 'Tên (A → Z)';
                break;
              case TaskSortOrder.titleDesc:
                title = 'Tên (Z → A)';
                break;
            }

            return ListTile(
              title: Text(title, style: const TextStyle(color: Colors.white)),
              onTap: () {
                context.read<TasksBloc>().add(ChangeTaskSort(sortOrder));
                Navigator.pop(context);
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  void _showDeleteCompletedDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.black87,
        title: const Text(
          'Xóa nhiệm vụ đã hoàn thành',
          style: TextStyle(color: Colors.white),
        ),
        content: const Text(
          'Bạn có chắc chắn muốn xóa tất cả nhiệm vụ đã hoàn thành?',
          style: TextStyle(color: Colors.white70),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Hủy', style: TextStyle(color: Colors.white70)),
          ),
          ElevatedButton(
            onPressed: () {
              context.read<TasksBloc>().add(const DeleteCompletedTasks());
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Xóa', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _addNewTask() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const AddEditTaskScreen(),
      ),
    );
  }

  void _showTaskDetails(Task task) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.black87,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        builder: (context, scrollController) => SingleChildScrollView(
          controller: scrollController,
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Center(
                child: Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.white54,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ),
              const SizedBox(height: 20),
              Text(
                task.title,
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
              ),
              const SizedBox(height: 16),
              Text(
                task.description,
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Colors.white70,
                    ),
              ),
              const SizedBox(height: 20),
              TaskCategoryChip(category: task.category),
              const SizedBox(height: 20),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () {
                        Navigator.pop(context);
                        _editTask(task);
                      },
                      icon: const Icon(Icons.edit),
                      label: const Text('Sửa'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () {
                        Navigator.pop(context);
                        _deleteTask(task);
                      },
                      icon: const Icon(Icons.delete),
                      label: const Text('Xóa'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _editTask(Task task) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddEditTaskScreen(task: task),
      ),
    );
  }

  void _deleteTask(Task task) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.black87,
        title: const Text(
          'Xóa nhiệm vụ',
          style: TextStyle(color: Colors.white),
        ),
        content: Text(
          'Bạn có chắc chắn muốn xóa nhiệm vụ "${task.title}"?',
          style: const TextStyle(color: Colors.white70),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Hủy', style: TextStyle(color: Colors.white70)),
          ),
          ElevatedButton(
            onPressed: () {
              context.read<TasksBloc>().add(DeleteTask(task.id));
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Xóa', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }
}
