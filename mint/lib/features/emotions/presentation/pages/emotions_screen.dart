import 'package:flutter/material.dart';

class EmotionsScreen extends StatelessWidget {
  const EmotionsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return SafeArea(child: Scaffold(body: _buildBody(context)));
  }

  Widget _buildBody(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        spacing: 15,
        children: [
          Container(
            decoration: BoxDecoration(
                border: Border.all(color: Colors.red),
                borderRadius: BorderRadius.all(Radius.circular(5))),
            child: Padding(
              padding: const EdgeInsets.all(12.0),
              child: Column(
                children: [
                  TextField(
                    decoration: InputDecoration(
                      hintText: "Chia sẻ với bạn bè: Tâm trạng bạn thế nào?",
                      border: InputBorder.none,
                    ),
                    maxLines: null,
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      IconButton(
                        icon: const Icon(Icons.edit, size: 20),
                        onPressed: () {},
                        tooltip: "Chỉnh sửa",
                      ),
                      IconButton(
                        icon: const Icon(Icons.image, size: 20),
                        onPressed: () {},
                        tooltip: "Thêm ảnh",
                      ),
                      IconButton(
                        icon:
                            const Icon(Icons.emoji_emotions_outlined, size: 20),
                        onPressed: () {},
                        tooltip: "Cảm xúc",
                      ),
                      IconButton(
                        icon: const Icon(Icons.settings, size: 20),
                        onPressed: () {},
                        tooltip: "Cài đặt",
                      ),
                      const Spacer(),
                      Row(
                        children: [
                          const SizedBox(width: 8),
                          ElevatedButton(
                            onPressed: () {},
                            child: const Text("Chia sẻ"),
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          Row(spacing: 15, children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(color: Colors.blue, width: 2),
              ),
            ),
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(color: Colors.blue, width: 2),
              ),
            ),
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(color: Colors.blue, width: 2),
              ),
            ),
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(color: Colors.blue, width: 2),
              ),
            ),
          ]),
          Center(
            child: Container(
              width: 400,
              margin: const EdgeInsets.symmetric(vertical: 24),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black12,
                    blurRadius: 8,
                    offset: Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Avatar
                      CircleAvatar(
                        radius: 22,
                        backgroundImage: NetworkImage(
                          'https://i.imgur.com/Avatarsample.png', // Thay bằng link avatar thật
                        ),
                      ),
                      const SizedBox(width: 10),
                      // Name & time
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Text(
                                  'The Avengers Việt Nam',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                  ),
                                ),
                                const SizedBox(width: 6),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 6, vertical: 2),
                                  decoration: BoxDecoration(
                                    color: Colors.blue[100],
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                  child: Text(
                                    'Theo dõi',
                                    style: TextStyle(
                                      color: Colors.blue[800],
                                      fontSize: 12,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 2),
                            Text(
                              '30 tháng 6 lúc 14:51 · ',
                              style: TextStyle(
                                  color: Colors.grey[600], fontSize: 12),
                            ),
                          ],
                        ),
                      ),
                      Icon(Icons.more_horiz, color: Colors.grey[700]),
                    ],
                  ),
                  const SizedBox(height: 10),
                  // Content
                  Text(
                    '_FACT_\nMấu chốt để có thể vận hành được 1 bộ giáp Đa Nhiệm như Iron Man đời hỏi phải có 1 nguồn năng lượng đủ lớn như Arc Reactor, 1 nguồn năng lượng mà đến...',
                    style: TextStyle(fontSize: 15),
                  ),
                  const SizedBox(height: 10),
                  // Images
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Column(
                      children: [
                        Image.network(
                          'https://i.imgur.com/ironman1.jpg', // Thay bằng link ảnh thật
                          width: double.infinity,
                          height: 180,
                          fit: BoxFit.cover,
                        ),
                        Row(
                          children: [
                            Expanded(
                              child: Image.network(
                                'https://i.imgur.com/arc_reactor.jpg', // Thay bằng link ảnh thật
                                height: 80,
                                fit: BoxFit.cover,
                              ),
                            ),
                            const SizedBox(width: 2),
                            Expanded(
                              child: Image.network(
                                'https://i.imgur.com/ironman_armors.jpg', // Thay bằng link ảnh thật
                                height: 80,
                                fit: BoxFit.cover,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 10),
                  // Reactions
                  Row(
                    children: [
                      Icon(Icons.emoji_emotions,
                          color: Colors.yellow[700], size: 20),
                      const SizedBox(width: 4),
                      Text('8,2K', style: TextStyle(color: Colors.grey[700])),
                      const Spacer(),
                      Text('1,1K bình luận',
                          style:
                              TextStyle(color: Colors.grey[700], fontSize: 13)),
                      const SizedBox(width: 8),
                      Text('208 lượt chia sẻ',
                          style:
                              TextStyle(color: Colors.grey[700], fontSize: 13)),
                    ],
                  ),
                  const Divider(height: 20),
                  // Action buttons
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _ActionButton(
                          icon: Icons.thumb_up_alt_outlined, label: 'Thích'),
                      _ActionButton(
                          icon: Icons.comment_outlined, label: 'Bình luận'),
                      _ActionButton(
                          icon: Icons.share_outlined, label: 'Chia sẻ'),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _ActionButton extends StatelessWidget {
  final IconData icon;
  final String label;
  const _ActionButton({required this.icon, required this.label});

  @override
  Widget build(BuildContext context) {
    return TextButton.icon(
      onPressed: () {},
      icon: Icon(icon, color: Colors.grey[700], size: 20),
      label: Text(label, style: TextStyle(color: Colors.grey[700])),
      style: TextButton.styleFrom(
        foregroundColor: Colors.grey[700],
        padding: const EdgeInsets.symmetric(horizontal: 12),
      ),
    );
  }
}
