import 'package:flutter/material.dart';

/// <PERSON><PERSON><PERSON> <PERSON><PERSON> nối
class ConnectionScreen extends StatelessWidget {
  const ConnectionScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      appBar: AppBar(
        title: const Text(
          'Kết nối',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        centerTitle: true,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header card
            _buildHeaderCard(context),
            
            const SizedBox(height: 20),
            
            // <PERSON><PERSON><PERSON> tùy chọn kết nối
            Expanded(
              child: _buildConnectionOptions(context),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeaderCard(BuildContext context) {
    return Card(
      color: Colors.black.withValues(alpha: 0.7),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          children: [
            const Icon(
              Icons.people,
              color: Colors.blue,
              size: 40,
            ),
            const SizedBox(height: 12),
            Text(
              'Kết nối với cộng đồng',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'Chia sẻ, học hỏi và hỗ trợ lẫn nhau trên hành trình chăm sóc sức khỏe tâm thần',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.white70,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildConnectionOptions(BuildContext context) {
    final options = [
      {
        'title': 'Cộng đồng hỗ trợ',
        'subtitle': 'Tham gia nhóm thảo luận và chia sẻ kinh nghiệm',
        'icon': Icons.forum,
        'color': Colors.blue,
        'onTap': () => _showCommunityScreen(context),
      },
      {
        'title': 'Tìm bạn đồng hành',
        'subtitle': 'Kết nối với những người có cùng mục tiêu',
        'icon': Icons.person_add,
        'color': Colors.green,
        'onTap': () => _showFindFriendsScreen(context),
      },
      {
        'title': 'Chuyên gia tư vấn',
        'subtitle': 'Liên hệ với các chuyên gia tâm lý',
        'icon': Icons.psychology,
        'color': Colors.purple,
        'onTap': () => _showExpertsScreen(context),
      },
      {
        'title': 'Chia sẻ câu chuyện',
        'subtitle': 'Kể câu chuyện của bạn để truyền cảm hứng',
        'icon': Icons.auto_stories,
        'color': Colors.orange,
        'onTap': () => _showShareStoryScreen(context),
      },
      {
        'title': 'Sự kiện & Hoạt động',
        'subtitle': 'Tham gia các sự kiện và hoạt động nhóm',
        'icon': Icons.event,
        'color': Colors.pink,
        'onTap': () => _showEventsScreen(context),
      },
      {
        'title': 'Hỗ trợ khẩn cấp',
        'subtitle': 'Liên hệ ngay khi cần hỗ trợ khẩn cấp',
        'icon': Icons.emergency,
        'color': Colors.red,
        'onTap': () => _showEmergencySupport(context),
      },
    ];

    return ListView.builder(
      itemCount: options.length,
      itemBuilder: (context, index) {
        final option = options[index];
        return _buildConnectionCard(context, option);
      },
    );
  }

  Widget _buildConnectionCard(BuildContext context, Map<String, dynamic> option) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      color: Colors.black.withValues(alpha: 0.6),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: option['color'].withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            option['icon'],
            color: option['color'],
            size: 30,
          ),
        ),
        title: Text(
          option['title'],
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        subtitle: Padding(
          padding: const EdgeInsets.only(top: 4),
          child: Text(
            option['subtitle'],
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 14,
            ),
          ),
        ),
        trailing: const Icon(
          Icons.arrow_forward_ios,
          color: Colors.white54,
          size: 16,
        ),
        onTap: option['onTap'],
      ),
    );
  }

  void _showCommunityScreen(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const CommunityDetailScreen(),
      ),
    );
  }

  void _showFindFriendsScreen(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Tính năng đang phát triển')),
    );
  }

  void _showExpertsScreen(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Tính năng đang phát triển')),
    );
  }

  void _showShareStoryScreen(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Tính năng đang phát triển')),
    );
  }

  void _showEventsScreen(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Tính năng đang phát triển')),
    );
  }

  void _showEmergencySupport(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.black87,
        title: const Text(
          'Hỗ trợ khẩn cấp',
          style: TextStyle(color: Colors.red, fontWeight: FontWeight.bold),
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Nếu bạn đang gặp khủng hoảng tâm lý, hãy liên hệ ngay:',
              style: TextStyle(color: Colors.white),
            ),
            SizedBox(height: 16),
            Text(
              '📞 Hotline: 1900-1234',
              style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              '🏥 Cấp cứu: 115',
              style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              '👮 Công an: 113',
              style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Đóng', style: TextStyle(color: Colors.white)),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // Mở ứng dụng gọi điện
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Gọi ngay', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }
}

/// Màn hình chi tiết cộng đồng
class CommunityDetailScreen extends StatelessWidget {
  const CommunityDetailScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        title: const Text('Cộng đồng hỗ trợ'),
        backgroundColor: Colors.transparent,
        foregroundColor: Colors.white,
      ),
      body: const Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          children: [
            Card(
              color: Colors.black87,
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Text(
                  'Tính năng cộng đồng đang được phát triển.\n\nSẽ bao gồm:\n• Diễn đàn thảo luận\n• Nhóm hỗ trợ\n• Chia sẻ kinh nghiệm\n• Tương tác với cộng đồng',
                  style: TextStyle(color: Colors.white, fontSize: 16),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
